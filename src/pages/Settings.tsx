import { useState } from "react";
import { Settings } from "lucide-react";
import "../App.css";

function SettingsPage() {
  // 图像编辑参数配置
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);
  const [inferenceSteps, setInferenceSteps] = useState<number>(20);
  const [randomSeed, setRandomSeed] = useState<string>("");

  // 硬编码的API密钥信息（仅用于显示配置状态）
  const douBaoApiKey = "2e3e1b1f-b6fb-46c7-a519-343d3146b88f"; // 豆包API密钥
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029"; // 阿里云API密钥

  // 硬编码的腾讯云COS配置信息（仅用于显示配置状态）
  const cosRegion = "ap-shanghai";
  const cosSecretId = "AKIDHdmkWCk6g3D9rMeiYCdpz1rVvINHxQis";
  const cosBucketName = "tennis-1258507500";

  // 保存设置的函数
  const saveSettings = () => {
    // 这里可以添加保存设置到本地存储或后端的逻辑
    alert("设置已保存！");
  };





  return (
    <div className="container">
      <h1>系统设置</h1>
      <p>配置AI图像处理工具的参数设置</p>

      <div className="config-section">
        <h2><Settings size={20} /> API配置状态</h2>

        {/* API密钥配置状态显示 */}
        <div className="form-group">
          <h3>豆包API配置</h3>
          <div className="upload-success">
            <p>✅ 豆包API密钥已配置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              密钥: {douBaoApiKey.substring(0, 8)}...{douBaoApiKey.substring(douBaoApiKey.length - 8)}
            </p>
          </div>
        </div>

        <div className="form-group">
          <h3>阿里云API配置</h3>
          <div className="upload-success">
            <p>✅ 阿里云API密钥已配置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              密钥: {aliyunApiKey.substring(0, 8)}...{aliyunApiKey.substring(aliyunApiKey.length - 8)}
            </p>
          </div>
        </div>

        <div className="form-group">
          <h3>腾讯云COS配置</h3>
          <div className="upload-success">
            <p>✅ 腾讯云COS配置已设置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              区域: {cosRegion} | 存储桶: {cosBucketName}
            </p>
          </div>
        </div>

        <h3>图像编辑参数设置</h3>
        <div className="form-group">
          <label>编辑强度 (0.0-1.0):</label>
          <input
            type="number"
            min="0.0"
            max="1.0"
            step="0.1"
            value={editStrength}
            onChange={(e) => setEditStrength(parseFloat(e.target.value))}
            className="api-key-input"
            placeholder="0.7"
          />
          <p className="info-text">
            💡 值越高，编辑效果越明显
          </p>
        </div>

        <div className="form-group">
          <label>引导比例 (1.0-20.0):</label>
          <input
            type="number"
            min="1.0"
            max="20.0"
            step="0.5"
            value={guidanceScale}
            onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
            className="api-key-input"
            placeholder="7.5"
          />
          <p className="info-text">
            💡 控制模型对提示词的遵循程度
          </p>
        </div>

        <div className="form-group">
          <label>推理步数 (10-50):</label>
          <input
            type="number"
            min="10"
            max="50"
            step="1"
            value={inferenceSteps}
            onChange={(e) => setInferenceSteps(parseInt(e.target.value))}
            className="api-key-input"
            placeholder="20"
          />
          <p className="info-text">
            💡 步数越多，质量越高但速度越慢
          </p>
        </div>

        <div className="form-group">
          <label>随机种子 (可选):</label>
          <input
            type="text"
            value={randomSeed}
            onChange={(e) => setRandomSeed(e.target.value)}
            className="api-key-input"
            placeholder="留空则随机生成"
          />
          <p className="info-text">
            💡 相同种子可以生成相似的结果
          </p>
        </div>

        {/* 腾讯云COS配置表单已隐藏 - 配置信息已硬编码到代码中 */}
        {/*
        原COS配置表单已移除，配置信息现在直接在代码中设置：
        - 区域: ap-shanghai
        - 访问凭据ID: AKIDHdmkWCk6g3D9rMeiYCdpz1rVvINHxQis
        - 凭据密钥: 3tWv64pOk9XzKXddWJyVwEOQL8oNn6IZ
        - 存储桶名称: tennis-1258507500
        - 自定义域名: (空)
        */}

        <div className="form-group">
          <label>图像编辑提示词:</label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="请输入图像编辑的提示词，例如：将天空改为夕阳，保持其他部分不变"
            className="prompt-textarea"
            rows={3}
          />
        </div>
      </div>

      <div className="upload-section">
        <h2><Upload size={20} /> 图片来源</h2>

        <div className="form-group">
          <label>图片URL（可选）:</label>
          <input
            type="text"
            value={manualImageUrl}
            onChange={handleManualUrlChange}
            placeholder="直接输入图片URL，例如: https://example.com/image.jpg"
            className="api-key-input"
          />
          {urlValidationError && (
            <div className="error-message error-message-small">
              <p>{urlValidationError}</p>
            </div>
          )}
          <p className="info-text">
            💡 提示：可以直接输入图片URL，或者上传本地文件到腾讯云COS
          </p>
        </div>

        <div className="form-group">
          <label>本地图片文件路径:</label>
          <input
            type="text"
            value={selectedFile}
            onChange={handleFilePathChange}
            placeholder="请输入图片文件的完整路径，例如: /Users/<USER>/Pictures/image.jpg"
            className="api-key-input"
          />
        </div>



        <button
          type="button"
          onClick={uploadFile}
          disabled={!selectedFile || isUploading}
          className="upload-btn"
        >
          {isUploading ? <Loader2 className="spinner" /> : <Upload size={20} />}
          {isUploading ? "上传中..." : "上传到腾讯云COS"}
        </button>

        {uploadedUrl && (
          <div className="upload-success">
            <p>✅ 文件上传成功！URL: {uploadedUrl}</p>
          </div>
        )}
      </div>

      <div className="process-section">
        <h2><Settings size={20} /> 图像编辑</h2>

        {/* 显示当前使用的图片来源 */}
        {(manualImageUrl.trim() || uploadedUrl) && (
          <div className="current-image-info">
            <p><strong>当前图片来源：</strong></p>
            {manualImageUrl.trim() ? (
              <p>🔗 手动输入URL: {manualImageUrl}</p>
            ) : (
              <p>📁 上传文件URL: {uploadedUrl}</p>
            )}
          </div>
        )}

        <button
          type="button"
          onClick={startImageEdit}
          disabled={(!uploadedUrl && !manualImageUrl.trim()) || !prompt || isProcessing || !!urlValidationError}
          className="process-btn"
        >
          {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
          {isProcessing ? "编辑中..." : "开始图像编辑"}
        </button>

        <div className="task-section">
          <div className="form-group">
            <label>任务ID:</label>
            <input
              type="text"
              value={taskId}
              onChange={(e) => setTaskId(e.target.value)}
              placeholder="任务ID会在创建任务后自动填入，也可手动输入"
              className="api-key-input"
            />
          </div>

          {taskId && (
            <button
              type="button"
              onClick={queryTaskResult}
              disabled={!taskId || isQuerying}
              className="query-btn"
            >
              {isQuerying ? <Loader2 className="spinner" /> : <Search size={16} />}
              {isQuerying ? "查询中..." : "查询任务结果"}
            </button>
          )}
        </div>
      </div>

      {resultImages.length > 0 && (
        <div className="results-section">
          <h2><Image size={20} /> 图像编辑结果</h2>
          <div className="result-images">
            {resultImages.map((url, index) => (
              <div key={index} className="result-item">
                <img
                  src={url}
                  alt={`编辑结果 ${index + 1}`}
                  className="result-image"
                />
                <div className="image-actions">
                  <button
                    onClick={() => downloadImage(url)}
                    className="download-btn"
                    type="button"
                  >
                    <Download size={16} />
                    查看原图
                  </button>
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="download-btn"
                    type="button"
                  >
                    <Image size={16} />
                    在新窗口打开
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}

export default SettingsPage;
