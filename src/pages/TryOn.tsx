import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Loader2, Image } from "lucide-react";

function TryOnPage() {
  const [modelImageUrl, setModelImageUrl] = useState("");
  const [garmentImageUrl, setGarmentImageUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImageUrl, setResultImageUrl] = useState("");
  const [error, setError] = useState("");

  // This would typically come from a shared state/context or props
  const [aliyunApiKey, setAliyunApiKey] = useState("");

  const handleStartTryOn = async () => {
    if (!aliyunApiKey || !modelImageUrl || !garmentImageUrl) {
      setError("Please provide Aliyun API Key (in Settings), model image URL, and garment image URL.");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResultImageUrl("");

    try {
      const result: any = await invoke("start_aliyun_tryon", {
        apiKey: aliyun<PERSON>pi<PERSON><PERSON>,
        imageUrl: modelImageUrl,
        garmentUrl: garmentImageUrl,
      });

      if (result.output.task_status === "SUCCEEDED" && result.output.image_url) {
        setResultImageUrl(result.output.image_url);
      } else {
        setError(`Processing failed: ${result.output.task_status}`);
      }
    } catch (err) {
      setError(`An error occurred: ${err}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div>
      <h1>AI Try-On</h1>
      <p>Provide a model image and a garment image to see the result.</p>
      
      {/* A temporary input for the API key until it's moved to a shared state */}
      <div className="form-group">
        <label>Aliyun API Key (temp):</label>
        <input
          type="password"
          value={aliyunApiKey}
          onChange={(e) => setAliyunApiKey(e.target.value)}
          placeholder="Enter Aliyun API Key here"
          className="api-key-input"
        />
      </div>

      <div className="form-group">
        <label>Model Image URL:</label>
        <input
          type="text"
          value={modelImageUrl}
          onChange={(e) => setModelImageUrl(e.target.value)}
          placeholder="https://example.com/model.jpg"
          className="api-key-input"
        />
      </div>

      <div className="form-group">
        <label>Garment Image URL:</label>
        <input
          type="text"
          value={garmentImageUrl}
          onChange={(e) => setGarmentImageUrl(e.target.value)}
          placeholder="https://example.com/garment.jpg"
          className="api-key-input"
        />
      </div>

      <button
        type="button"
        onClick={handleStartTryOn}
        disabled={isProcessing}
        className="process-btn"
      >
        {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
        {isProcessing ? "Processing..." : "Start AI Try-On"}
      </button>

      {error && (
        <div className="error-message">
          <p>Error: {error}</p>
        </div>
      )}

      {resultImageUrl && (
        <div className="results-section">
          <h2>Result</h2>
          <img src={resultImageUrl} alt="AI Try-On Result" className="result-image" />
        </div>
      )}
    </div>
  );
}

export default TryOnPage;
