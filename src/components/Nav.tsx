import { NavLink } from "react-router-dom";
import "./Nav.css";

function Nav() {
  return (
    <nav className="nav">
      <NavLink to="/" className={({ isActive }) => (isActive ? "nav-link active" : "nav-link")}>
        Settings
      </NavLink>
      <NavLink to="/try-on" className={({ isActive }) => (isActive ? "nav-link active" : "nav-link")}>
        AI Try-On
      </NavLink>
    </nav>
  );
}

export default Nav;
