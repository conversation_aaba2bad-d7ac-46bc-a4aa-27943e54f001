import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SettingsPage from "./pages/Settings";
import TryOnPage from "./pages/TryOn";
import Nav from "./components/Nav";
import "./App.css";

function App() {
  return (
    <Router>
      <main className="container">
        <Nav />
        <Routes>
          <Route path="/" element={<SettingsPage />} />
          <Route path="/try-on" element={<TryOnPage />} />
        </Routes>
      </main>
    </Router>
  );
}

export default App;
